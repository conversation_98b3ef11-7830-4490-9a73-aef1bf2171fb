# NAND Flash Die测试分选系统需求分析文档

## 1. 引言与项目概述

### 1.1 目的
本文档概述了NAND Flash Die测试分选系统的需求。它作为项目利益相关者、开发人员和测试人员在整个开发生命周期中的参考依据。

### 1.2 项目范围
NAND Flash Die测试分选系统旨在提供一个自动化的测试平台，用于对NAND Flash芯片的Die进行测试、分类和分选。该系统将执行一系列测试操作码，评估每个Die的性能和质量，并根据预设标准对其进行分类，以支持后续的封装和质量控制流程。

### 1.3 系统背景
NAND Flash芯片在生产过程中，从晶圆(Wafer)制作完成后需要进行切片测试，将Flash Die分为合格和不合格。本系统作为芯片测试流程中的关键环节，通过执行特定的操作码和测试流程，确保只有合格的Die才能进入下一阶段的封装过程，从而提高最终产品的质量和可靠性。

## 2. 利益相关者与用户角色

### 2.1 利益相关者
- 模组生产商：利用测试数据选择合适的方案搭配
- 销售部门: 为主控产品赋能
- 研发部门: 利用此系统快速了解客户Die的品质

### 2.2 用户角色
- 测试操作员: 负责操作测试设备，执行测试流程，记录测试结果
- 测试工程师: 负责配置测试参数，分析测试结果，优化测试流程
- 数据分析师: 负责分析测试数据，生成报告，提供决策支持

## 3. 功能需求

### 3.1 核心功能
- FR-1: 系统应能够自动执行NAND Flash Die的测试操作码，包括读取、写入、擦除等基本操作
- FR-2: 系统应能够支持多Die并行测试，提高测试效率
- FR-3: 系统应能够根据预设的测试流程和参数，对每个Die执行完整的测试序列
- FR-4: 系统应能够实时监控测试过程，检测异常情况并做出相应处理
- FR-5: 系统应能够根据测试结果，对Die进行分类和分选（如良品、次品、报废等）
- FR-6: 系统应支持不同型号NAND Flash芯片的测试需求，能够加载不同的测试配置
- FR-7: 系统应支持ECC选择、测试BLOCK数量选择、强制NandFlash ID
- FR-8: 系统应支持BIN级自定义设定
- FR-9: 系统应支预留解Code功能接口，方便后期扩展解Code功能

### 3.2 管理功能
- FR-A1: 系统应提供授权机制，确保只有授权机器才能操作系统
- FR-A2: 系统应提供测试参数和流程的配置界面，允许测试工程师调整测试策略
- FR-A3: 系统应提供测试日志管理功能，记录所有测试活动和系统操作
- FR-A4: 系统应提供测试数量管控功能，使用usb key进行测试数量管控

### 3.3 报告功能
- FR-R1: 系统应能够生成详细的测试报告，包括测试结果、统计数据和异常情况
- FR-R2: 系统应提供良品率分析功能，帮助识别生产问题和改进机会
- FR-R3: 系统应能够导出测试数据，支持与其他系统的数据交换
- FR-R4: 系统应提供实时监控面板，显示当前测试状态和关键指标

### 3.4 用户界面
- FR-U1: 系统主界面应该显示用户设置的主要信息
- FR-U2：系统主界面应该有8个文本框，显示对应Die的信息，测试状态 
- FR-U3：系统主界面应该有8个按钮，用于弹出单端口的操作菜单
- FR-U4：系统右侧应该有开始、停止、刷新、设置按钮
- FR-U5：系统底部应该有状态栏，显示当前测试的数量、良率、每个BIN级的数量




## 4. 非功能需求

### 4.1 性能需求
- NFR-P1: 系统应能够同时测试至少8/16个Die，以提高测试吞吐量
- NFR-P2: 系统响应时间应不超过1秒，确保实时监控和控制
- NFR-P3: 系统应能够在24小时内完成至少2000个Die的测试

### 4.2 安全需求
- NFR-S1: 所有用户操作应记录在安全日志中，支持审计追踪
- NFR-S2: 敏感测试数据应进行加密存储，防止未授权访问
- NFR-S3: 系统应支持定期备份测试数据和配置信息

### 4.3 可用性需求
- NFR-U1: 系统界面应简洁直观，操作员经过不超过4小时培训即可熟练操作
- NFR-U2: 系统应提供中文和英文双语界面，支持国际化
- NFR-U3: 系统应提供上下文帮助和用户手册，便于用户学习和参考
- NFR-U4: 系统应支持快捷键和批处理操作，提高操作效率

### 4.4 可靠性需求
- NFR-R1: 系统应保持99.5%以上的可用性，年计划外停机时间不超过44小时
- NFR-R2: 系统应能够在硬件故障时自动保存测试数据，防止数据丢失
- NFR-R3: 系统应能够检测和报告测试设备的异常状态，防止错误测试

### 4.5 匹配度需求
- NFR-M1: YH2110 BIN1/BIN2/BIN3 90%
- NFR-M2: YH2100 BIN1/BIN2/BIN3 90%
- NFR-M3: YH1100 BIN1/BIN2/BIN3 90%
- NFR-M4: YH1000 BIN2/BIN3/BIN4 85%

### 4.6 测试时间需求
- NFR-T1: 单Die测试时间要求表

|TLC |时间 |QLC |  时间  |
|----|----|----|------- |
| 64G|1MIN|64G |小于2MIN|
|128G|2MIN|128G|小于4MIN|
|    |    |256G|小于5MIN|

## 5. 系统约束

### 5.1 硬件约束
- 系统应兼容现有的NAND Flash测试设备和接口
- 系统应支持至少8通道的并行测试能力
- 系统应能够在工业环境下稳定运行，适应温度范围为10°C至40°C
- 系统应配备足够的存储空间，能够存储至少6个月的测试数据

### 5.2 软件约束
- 系统应基于Windows 10或更高版本的操作系统开发
- 系统应使用C++/C#等高性能编程语言开发核心测试模块
- 系统应采用模块化设计，便于维护和升级

## 6. 用例/用户故事

### 6.1 用例1: 执行Die测试流程
**参与者**: 测试操作员
**描述**: 测试操作员启动并执行NAND Flash Die的自动测试流程
**前置条件**:
- 测试设备已正确连接并校准
- 待测Die已正确放置在测试位置
- 测试参数和流程已配置完成
**主要流程**:
1. 测试操作员登录系统
2. 选择测试配置和参数
3. 启动自动测试流程
4. 系统执行测试操作码序列
5. 系统收集和分析测试数据
6. 系统根据测试结果对Die进行分类
7. 系统生成测试报告
**替代流程**:
- 如果测试过程中发现异常，系统暂停测试并提示操作员
- 如果测试设备出现故障，系统记录错误并安全停止测试
**后置条件**:
- 测试结果已保存到数据库
- Die已根据测试结果进行分类
- 测试报告已生成

### 6.2 用例2: 配置测试参数和流程
**参与者**: 测试工程师
**描述**: 测试工程师配置和优化测试参数和流程
**前置条件**:
- 测试工程师已登录系统并具有配置权限
- 系统已安装必要的测试库和工具
**主要流程**:
1. 测试工程师选择芯片型号
2. 配置测试操作码序列
3. 设置测试参数（电压、时序等）
4. 定义测试通过/失败的标准
5. 保存测试配置
6. 验证测试配置的有效性
**替代流程**:
- 如果配置验证失败，系统提示错误并允许修正
- 测试工程师可以从模板或历史配置中导入基础设置
**后置条件**:
- 新的测试配置已保存并可用于测试
- 配置变更已记录在系统日志中

### 6.3 用例3: 分析测试数据和生成报告
**参与者**: 数据分析师
**描述**: 数据分析师分析测试结果并生成报告
**前置条件**:
- 测试数据已收集并存储在系统中
- 数据分析师具有数据访问权限
**主要流程**:
1. 数据分析师选择时间范围和芯片型号
2. 系统检索相关测试数据
3. 分析师应用统计工具分析良品率和失效模式
4. 系统生成图表和趋势分析
5. 分析师添加注释和解释
6. 生成并导出最终报告
**替代流程**:
- 如果数据不足，系统提示需要更多样本
- 分析师可以设置自动报告生成规则
**后置条件**:
- 分析报告已生成并可供相关人员查阅
- 分析结果已用于改进测试流程和生产工艺

## 13. 术语表

| 术语 | 定义 |
|------|------------|
| NAND Flash | 一种非易失性存储器，广泛应用于固态硬盘、USB闪存盘等存储设备 |
| Die | 晶圆上的单个芯片单元，是芯片制造的基本单位 |
| Wafer | 晶圆，半导体制造中的薄片基底，上面制作多个相同的Die |
| KGD | Known Good Die，已知良好的Die，通过测试确认性能符合要求的Die |
| 操作码 | 发送给NAND Flash的命令代码，用于执行特定操作，如读取、写入、擦除等 |
| Multi-Plane操作 | 在NAND Flash的多个Plane上并行执行操作，提高性能的技术 |
| Interleaved Die操作 | 在多个Die之间交错执行操作，提高并行度和性能的技术 |
| ECC | Error Checking and Correction，错误检查和纠正，用于检测和修复NAND Flash中的位错误 |
| Burn-in测试 | 在特定条件下（高温/高电压/一定湿度）加速芯片老化的测试方法，用于筛选早期失效的产品 |
| TTR | Test Time Reduction，测试时间减少，优化测试流程以减少测试时间的技术 |
| DFT | Design For Testability，可测试性设计，在芯片设计阶段考虑测试需求的方法 |

## 14. 批准

| 姓名 | 角色 | 签名 | 日期 |
|------|------|-----------|------|
| ____________ | 项目经理 | ____________ | ________ |
| ____________ | 测试部门经理 | ____________ | ________ |
| ____________ | 质量保证经理 | ____________ | ________ |
| ____________ | 研发部门经理 | ____________ | ________ |
